import Biography from "@/components/home/<USER>";
import Collaborators from "@/components/home/<USER>";
import CTA from "@/components/home/<USER>";
import Filmography from "@/components/home/<USER>";
import Hero from "@/components/home/<USER>";
import Press from "@/components/home/<USER>";
import ShowreelHome from "@/components/home/<USER>";
import { cancelFrame, frame } from "framer-motion";
import React<PERSON>enis, { LenisRef } from "lenis/react";
import { useEffect, useRef } from "react";

export default function Home() {
  const lenisRef = useRef<LenisRef>(null);

  useEffect(() => {
    function update(data: { timestamp: number }) {
      const time = data.timestamp;
      lenisRef.current?.lenis?.raf(time);
    }

    frame.update(update, true);

    return () => cancelFrame(update);
  }, []);

  return (
    <ReactLenis root options={{ autoRaf: false }} ref={lenisRef}>
      <Hero />
      <Biography />
      <ShowreelHome />
      <Filmography />
      <Press />
      <Collaborators />
      <CTA />
    </ReactLenis>
  );
}
